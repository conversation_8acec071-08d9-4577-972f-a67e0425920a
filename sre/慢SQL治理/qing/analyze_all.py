#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
慢查询完整分析流程自动化脚本

用法：
    python3 analyze_all.py <Excel文件名> <对比基线日期>
    
参数：
    Excel文件名: 慢查询Excel文件
    对比基线日期: 格式YYYY-MM-DD，用于对比的历史数据日期（必填）
    
示例：
    python3 analyze_all.py idp_export_idp_23817816_企业应用慢查支持.xlsx 2025-09-09
"""

import pandas as pd
import json
import csv
import sys
import os
import shutil
import re
from datetime import datetime
from collections import defaultdict

# 1. 加白的集群列表（对应main.go中的EXCLUDE_CLUSTERS）
whitelist_clusters = ["hrdpKdbTest", "eeDataHrpa"]
    
# 2. 加白的数据库列表（对应main.go中的EXCLUDE_DATABASES）
whitelist_databases = [
    # "test_db",  # 可根据需要添加
]
    
# 3. 加白的标签列表（对应main.go中的DEFAULT_WHITELIST_CONFIG.WhitelistTags）
whitelist_tags = [
    "KWAIBI_QUERY",  # BI查询相关
    "DP_SYNC_DATA",  # 数据同步相关
]


def log_step(step_name):
    """打印步骤标题"""
    print(f"\n{'='*70}")
    print(f"🔹 {step_name}")
    print(f"{'='*70}")


def extract_excel_data(excel_file):
    """步骤1: 从Excel文件中提取数据"""
    log_step("步骤1: 提取Excel数据")
    
    if not os.path.exists(excel_file):
        print(f"❌ 错误: 文件不存在: {excel_file}")
        return None
    
    try:
        df = pd.read_excel(excel_file)
        print(f"✅ 成功读取Excel文件，共{len(df)}行，{len(df.columns)}列")
        
        records = []
        for index, row in df.iterrows():
            try:
                record = {
                    "first_department": str(row.iloc[1]) if pd.notna(row.iloc[1]) else "",
                    "second_department": str(row.iloc[2]) if pd.notna(row.iloc[2]) else "",
                    "third_department": str(row.iloc[3]) if pd.notna(row.iloc[3]) else "",
                    "service_category": str(row.iloc[4]) if pd.notna(row.iloc[4]) else "",
                    "work_order_id": str(row.iloc[8]) if pd.notna(row.iloc[8]) else "",
                    "query_id": str(row.iloc[11]) if pd.notna(row.iloc[11]) else "",
                    "database_cluster": str(row.iloc[10]) if pd.notna(row.iloc[10]) else "",
                    "identity": str(row.iloc[12]) if pd.notna(row.iloc[12]) else "",
                    "sql_statement": str(row.iloc[13]) if pd.notna(row.iloc[13]) else "",
                    "tag_list": str(row.iloc[15]) if pd.notna(row.iloc[15]) else "",
                    "total_count": round(float(row.iloc[27])) if pd.notna(row.iloc[27]) else 0,
                    "total_query_time": float(row.iloc[28]) if pd.notna(row.iloc[28]) and str(row.iloc[28]).replace('.','').replace('-','').isdigit() else 0.0,
                    "avg_query_time": float(row.iloc[30]) if pd.notna(row.iloc[30]) and str(row.iloc[30]).replace('.','').replace('-','').isdigit() else 0.0,
                    "max_lock_time": float(row.iloc[31]) if pd.notna(row.iloc[31]) and str(row.iloc[31]).replace('.','').replace('-','').isdigit() else 0.0,
                    "avg_lock_time": float(row.iloc[32]) if pd.notna(row.iloc[32]) and str(row.iloc[32]).replace('.','').replace('-','').isdigit() else 0.0,
                    "max_rows_examined": round(float(row.iloc[33])) if pd.notna(row.iloc[33]) else 0,
                    "avg_rows_examined": round(float(row.iloc[34])) if pd.notna(row.iloc[34]) else 0,
                    "max_rows_sent": round(float(row.iloc[35])) if pd.notna(row.iloc[35]) else 0,
                    "avg_rows_sent": round(float(row.iloc[36])) if pd.notna(row.iloc[36]) else 0,
                    "status": str(row.iloc[18]) if pd.notna(row.iloc[18]) else "",
                    "owner": str(row.iloc[19]) if pd.notna(row.iloc[19]) else "",
                    "comments": str(row.iloc[20]) if pd.notna(row.iloc[20]) else "",
                    "deadline": str(row.iloc[21]) if pd.notna(row.iloc[21]) else "",
                    "first_seen": str(row.iloc[22]) if pd.notna(row.iloc[22]) else "",
                    "last_seen": str(row.iloc[23]) if pd.notna(row.iloc[23]) else "",
                }
                records.append(record)
            except Exception as e:
                continue
        
        print(f"✅ 成功处理{len(records)}条记录")
        return records, excel_file
        
    except Exception as e:
        print(f"❌ 处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def check_whitelist(record):
    """检查是否加白 - 与main.go中的逻辑保持一致"""
    
    # 检查集群加白
    cluster_name = record.get('identity', '') or record.get('database_cluster', '')
    for wl_cluster in whitelist_clusters:
        if wl_cluster in str(cluster_name):
            return True, f"测试集群加白: {wl_cluster}"
    
    # 检查数据库加白
    database_names = record.get('database_names', [])
    if isinstance(database_names, str):
        database_names = [database_names]
    
    for db_name in database_names:
        for wl_db in whitelist_databases:
            if wl_db in str(db_name):
                return True, f"测试数据库加白: {wl_db}"
    
    # 检查标签加白
    tag_list = record.get('tag_list', [])
    if isinstance(tag_list, str):
        # tag_list 可能是JSON字符串，需要解析
        try:
            import json
            tag_list = json.loads(tag_list)
        except:
            tag_list = [tag_list]
    
    for tag in tag_list:
        for wl_tag in whitelist_tags:
            if wl_tag in str(tag):
                return True, f"标签加白: {wl_tag}"
    
    return False, ""


def save_json_data(records, source_file, date_folder):
    """步骤2: 保存数据为JSON格式"""
    log_step("步骤2: 保存JSON数据")
    
    output_data = {
        "total_count": len(records),
        "extracted_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "source_file": source_file,
        "records": records
    }
    
    # 统一使用固定文件名，不带时间戳
    json_filename = os.path.join(date_folder, 'full_excel_data.json')
    
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 数据已保存为JSON: {json_filename}")
    return json_filename


def generate_cluster_analysis(records, date_folder):
    """步骤3: 生成集群维度汇聚分析"""
    log_step("步骤3: 集群维度汇聚分析")
    
    cluster_stats = defaultdict(lambda: {
        'count': 0,
        'cluster_ids': set(),
        'departments': set(),
        'owners': set(),
        'work_orders': set()
    })
    
    for record in records:
        cluster_id = record.get('database_cluster', '')
        identity = record.get('identity', '')
        cluster_name = identity if identity else cluster_id
        
        if not cluster_name:
            continue
        
        cluster_stats[cluster_name]['count'] += 1
        if cluster_id:
            cluster_stats[cluster_name]['cluster_ids'].add(cluster_id)
        if record.get('first_department'):
            cluster_stats[cluster_name]['departments'].add(record['first_department'])
        if record.get('owner'):
            cluster_stats[cluster_name]['owners'].add(record['owner'])
        if record.get('work_order_id'):
            cluster_stats[cluster_name]['work_orders'].add(record['work_order_id'])
    
    total_queries = sum(stats['count'] for stats in cluster_stats.values())
    sorted_clusters = sorted(cluster_stats.items(), key=lambda x: x[1]['count'], reverse=True)
    
    print(f"   总集群数量: {len(cluster_stats)}")
    print(f"   总慢查询数量: {total_queries:,}")
    
    # 生成CSV，统一文件名
    csv_filename = os.path.join(date_folder, '慢查询集群维度分析.csv')
    with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = ['排名', '集群名称', '慢查询数量', '占比(%)', '关联的集群ID', '涉及部门', '负责人数量', '负责人列表', '工单数量']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for i, (cluster_name, stats) in enumerate(sorted_clusters, 1):
            percentage = (stats['count'] / total_queries) * 100
            writer.writerow({
                '排名': i,
                '集群名称': cluster_name,
                '慢查询数量': stats['count'],
                '占比(%)': round(percentage, 2),
                '关联的集群ID': '; '.join(sorted(list(stats['cluster_ids']))),
                '涉及部门': '; '.join(list(stats['departments'])),
                '负责人数量': len(stats['owners']),
                '负责人列表': '; '.join(sorted(list(stats['owners']))),
                '工单数量': len(stats['work_orders'])
            })
    
    print(f"✅ 已生成: {os.path.basename(csv_filename)}")
    
    # 生成Markdown，统一文件名
    md_filename = os.path.join(date_folder, '慢查询集群维度分析报告.md')
    with open(md_filename, 'w', encoding='utf-8') as mdfile:
        mdfile.write(f'# 慢查询集群维度汇聚分析报告\n\n')
        mdfile.write(f'**生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n')
        mdfile.write(f'## 总体统计\n\n')
        mdfile.write(f'- 总集群数量: {len(cluster_stats)}\n')
        mdfile.write(f'- 总慢查询数量: {total_queries:,}\n\n')
        mdfile.write(f'## 完整集群列表\n\n')
        mdfile.write(f'| 排名 | 集群名称 | 慢查询数量 | 占比(%) | 关联的集群ID | 涉及部门 | 负责人数量 | 工单数量 |\n')
        mdfile.write(f'|------|----------|------------|---------|--------------|----------|------------|----------|\n')
        
        for i, (cluster_name, stats) in enumerate(sorted_clusters, 1):
            percentage = (stats['count'] / total_queries) * 100
            mdfile.write(f'| {i} | {cluster_name} | {stats["count"]:,} | {percentage:.2f}% | {"; ".join(sorted(list(stats["cluster_ids"])))} | {"; ".join(list(stats["departments"]))} | {len(stats["owners"])} | {len(stats["work_orders"]):,} |\n')
    
    print(f"✅ 已生成: {os.path.basename(md_filename)}")
    
    return sorted_clusters[:10]


def generate_owner_analysis(records, date_folder):
    """步骤4: 生成负责人维度分析（按人拆分）"""
    log_step("步骤4: 负责人维度分析（按人拆分）")
    
    owner_stats = defaultdict(lambda: {
        'records': [],
        'total_count': 0,
        'whitelisted_count': 0
    })
    
    # 按负责人汇总（只统计未加白的SQL）
    for record in records:
        owner = record.get('owner', '').strip()
        if not owner or owner == 'nan':
            owner = '未分配'
        
        is_whitelisted, whitelist_reason = check_whitelist(record)
        
        # 只处理未加白的SQL
        if not is_whitelisted:
            record['is_whitelisted'] = is_whitelisted
            record['whitelist_reason'] = whitelist_reason
            
            owner_stats[owner]['records'].append(record)
            owner_stats[owner]['total_count'] += 1
        else:
            # 统计加白数量，但不添加到记录中
            if owner in owner_stats:
                owner_stats[owner]['whitelisted_count'] += 1
    
    print(f"   共有 {len(owner_stats)} 个负责人")
    print(f"   正在生成各负责人的SQL明细...")
    
    # 创建负责人明细目录
    owner_detail_dir = os.path.join(date_folder, '负责人SQL明细')
    os.makedirs(owner_detail_dir, exist_ok=True)
    
    # 为每个负责人生成Excel文件
    owner_summary = []
    
    for owner, stats in owner_stats.items():
        # 按执行次数排序（降序）
        stats['records'].sort(key=lambda x: x.get('total_count', 0), reverse=True)
        
        # 生成Excel文件（使用openpyxl创建超链接）
        excel_filename = os.path.join(owner_detail_dir, f'{owner}_慢SQL明细.xlsx')
        
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment
        
        wb = Workbook()
        ws = wb.active
        ws.title = f"{owner}的慢SQL"
        
        # 定义表头
        headers = ['序号', '查看优化建议', '工单ID', '数据库集群', '执行次数', 
                   '总执行时间(ms)', '平均执行时间(ms)', '最大扫描行数', '平均扫描行数', 
                   '标签', 'SQL语句', '状态', '备注', '首次出现', '最后出现']
        ws.append(headers)
        
        # 设置表头样式
        for cell in ws[1]:
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 填充数据
        for idx, record in enumerate(stats['records'], 1):
            cluster_id = record.get('database_cluster', '')
            query_id = record.get('query_id', '')
            
            # 构建优化建议链接
            advise_url = f"https://halo.corp.kuaishou.com/mysql/page/slowLog-advise?cluster_id={cluster_id}&query_id={query_id}"
            
            row_data = [
                idx,
                advise_url,  # 先填充URL，后面会转换为超链接
                record['work_order_id'],
                record.get('identity', '') or cluster_id,
                record['total_count'],
                record['total_query_time'],
                record['avg_query_time'],
                record['max_rows_examined'],
                record['avg_rows_examined'],
                record['tag_list'],
                record['sql_statement'][:500],  # 限制长度
                record['status'],
                record['comments'],
                record['first_seen'],
                record['last_seen']
            ]
            ws.append(row_data)
            
            # 为"查看优化建议"列添加超链接
            row_num = idx + 1  # 加1是因为第一行是表头
            cell = ws.cell(row=row_num, column=2)  # 第2列是"查看优化建议"
            cell.hyperlink = advise_url
            cell.value = "点击查看"
            cell.font = Font(color="0563C1", underline="single")  # 蓝色下划线
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 调整列宽
        ws.column_dimensions['A'].width = 8   # 序号
        ws.column_dimensions['B'].width = 15  # 查看优化建议
        ws.column_dimensions['C'].width = 12  # 工单ID
        ws.column_dimensions['D'].width = 20  # 数据库集群
        ws.column_dimensions['E'].width = 12  # 执行次数
        ws.column_dimensions['F'].width = 15  # 总执行时间
        ws.column_dimensions['G'].width = 15  # 平均执行时间
        ws.column_dimensions['H'].width = 15  # 最大扫描行数
        ws.column_dimensions['I'].width = 15  # 平均扫描行数
        ws.column_dimensions['J'].width = 40  # 标签
        ws.column_dimensions['K'].width = 60  # SQL语句
        ws.column_dimensions['L'].width = 12  # 状态
        ws.column_dimensions['M'].width = 20  # 备注
        ws.column_dimensions['N'].width = 20  # 首次出现
        ws.column_dimensions['O'].width = 20  # 最后出现
        
        # 保存文件
        wb.save(excel_filename)
        
        owner_summary.append({
            '负责人': owner,
            '需治理SQL数': stats['total_count'],  # 现在只统计未加白的
            '文件名': f'{owner}_慢SQL明细.xlsx'
        })
        
        print(f"   ✅ {owner}: {stats['total_count']} 条需治理的慢SQL")
    
    # 生成汇总表，统一文件名
    summary_filename = os.path.join(date_folder, '负责人慢SQL汇总.csv')
    summary_df = pd.DataFrame(owner_summary)
    summary_df = summary_df.sort_values('需治理SQL数', ascending=False)
    summary_df.to_csv(summary_filename, index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 已生成负责人汇总: {os.path.basename(summary_filename)}")
    print(f"✅ 已生成 {len(owner_stats)} 个负责人的SQL明细文件")
    
    return owner_summary[:10]


def generate_distribution_analysis(records, date_folder):
    """新增功能: 生成负责人慢查执行次数分布分析HTML报告"""
    log_step("新增功能: 负责人慢查执行次数分布分析")
    
    # 定义执行次数档位
    bins = [0, 2, 5, 10, 50, 100, 300, 500, 1000, float('inf')]
    bin_labels = ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次']
    
    # 整体分布统计（不分负责人）
    overall_distribution = defaultdict(int)
    overall_total = 0
    
    # 按负责人汇总
    owner_stats = defaultdict(lambda: {
        'records': [],
        'distribution': defaultdict(int),
        'total_count': 0
    })
    
    for record in records:
        owner = record.get('owner', '').strip()
        if not owner or owner == 'nan':
            owner = '未分配'
        
        # 跳过加白的SQL
        is_whitelisted, _ = check_whitelist(record)
        if is_whitelisted:
            continue
        
        exec_count = record.get('total_count', 0)
        owner_stats[owner]['records'].append(record)
        owner_stats[owner]['total_count'] += 1
        
        # 统计整体分布
        overall_total += 1
        
        # 确定执行次数所属档位
        for i in range(len(bins) - 1):
            if bins[i] < exec_count <= bins[i + 1]:
                owner_stats[owner]['distribution'][bin_labels[i]] += 1
                overall_distribution[bin_labels[i]] += 1
                break
    
    print(f"   分析 {len(owner_stats)} 个负责人的执行次数分布...")
    print(f"   整体慢查总数: {overall_total} 条")
    
    # 生成HTML报告
    html_filename = os.path.join(date_folder, '负责人慢查执行次数分布分析.html')
    
    # 准备数据用于生成图表
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>负责人慢查执行次数分布分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        .header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .summary-card .value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        .summary-card .label {
            font-size: 14px;
            color: #666;
        }
        .content {
            padding: 30px;
        }
        .owner-section {
            margin-bottom: 40px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .owner-header {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .owner-header .count {
            font-size: 14px;
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
        }
        .chart-container {
            padding: 20px;
            background: #fafafa;
        }
        canvas {
            max-height: 300px;
        }
        .distribution-table {
            width: 100%;
            margin-top: 20px;
            border-collapse: collapse;
        }
        .distribution-table th,
        .distribution-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        .distribution-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .distribution-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .distribution-table tr:hover {
            background: #e8eaf6;
        }
        .percentage {
            color: #666;
            font-size: 12px;
        }
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 负责人慢查执行次数分布分析</h1>
            <div class="subtitle">生成时间: ''' + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '''</div>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <div class="label">负责人总数</div>
                <div class="value">''' + str(len(owner_stats)) + '''</div>
            </div>
            <div class="summary-card">
                <div class="label">慢查总数（未加白）</div>
                <div class="value">''' + str(sum(s['total_count'] for s in owner_stats.values())) + '''</div>
            </div>
            <div class="summary-card">
                <div class="label">平均每人慢查数</div>
                <div class="value">''' + str(round(sum(s['total_count'] for s in owner_stats.values()) / len(owner_stats) if owner_stats else 0)) + '''</div>
            </div>
        </div>
        
        <div class="content">
            <!-- 整体分布图表 -->
            <div class="owner-section" style="background: linear-gradient(135deg, #f6d365 0%, #fda085 100%); border: 2px solid #ff8c42;">
                <div class="owner-header" style="background: linear-gradient(90deg, #ff8c42 0%, #ff6b6b 100%);">
                    <span>🌟 整体执行次数分布（所有负责人汇总）</span>
                    <span class="count">总计: ''' + str(overall_total) + ''' 条慢查</span>
                </div>
                <div class="chart-container" style="background: white;">
                    <canvas id="chartOverall"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
'''
    
    # 添加整体分布表头
    for label in bin_labels:
        html_content += f'<th>{label}</th>'
    
    html_content += '''
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
'''
    
    # 添加整体分布数据
    for label in bin_labels:
        count = overall_distribution.get(label, 0)
        percentage = (count / overall_total * 100) if overall_total > 0 else 0
        html_content += f'<td>{count}<br><span class="percentage">({percentage:.1f}%)</span></td>'
    
    html_content += '''
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 分隔线 -->
            <div style="margin: 40px 0; text-align: center; color: #999; font-size: 20px; font-weight: bold;">
                ━━━━━━━━━━━━━━━━━━ 各负责人明细分布 ━━━━━━━━━━━━━━━━━━
            </div>
'''
    
    # 按负责人的慢查总数排序
    sorted_owners = sorted(owner_stats.items(), key=lambda x: x[1]['total_count'], reverse=True)
    
    chart_id = 0
    for owner, stats in sorted_owners:
        chart_id += 1
        
        # 准备图表数据
        distribution_data = []
        for label in bin_labels:
            distribution_data.append(stats['distribution'].get(label, 0))
        
        html_content += f'''
            <div class="owner-section">
                <div class="owner-header">
                    <span>{owner}</span>
                    <span class="count">慢查总数: {stats['total_count']}</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart{chart_id}"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
'''
        
        for label in bin_labels:
            html_content += f'<th>{label}</th>'
        
        html_content += '''
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
'''
        
        for label in bin_labels:
            count = stats['distribution'].get(label, 0)
            percentage = (count / stats['total_count'] * 100) if stats['total_count'] > 0 else 0
            html_content += f'<td>{count}<br><span class="percentage">({percentage:.1f}%)</span></td>'
        
        html_content += '''
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
'''
    
    html_content += '''
        </div>
        
        <div class="footer">
            © 2025 慢SQL治理分析系统 | 本报告用于内部优化决策参考
        </div>
    </div>
    
    <script>
'''
    
    # 先生成整体分布图表的JavaScript
    overall_distribution_data = []
    for label in bin_labels:
        overall_distribution_data.append(overall_distribution.get(label, 0))
    
    html_content += f'''
        // 整体分布图表
        const ctxOverall = document.getElementById('chartOverall').getContext('2d');
        new Chart(ctxOverall, {{
            type: 'bar',
            data: {{
                labels: {bin_labels},
                datasets: [{{
                    label: '慢查数量',
                    data: {overall_distribution_data},
                    backgroundColor: [
                        'rgba(255, 140, 66, 0.9)',
                        'rgba(255, 107, 107, 0.9)',
                        'rgba(255, 82, 82, 0.9)',
                        'rgba(255, 177, 66, 0.9)',
                        'rgba(255, 200, 87, 0.9)',
                        'rgba(252, 227, 138, 0.9)',
                        'rgba(254, 202, 87, 0.9)',
                        'rgba(255, 159, 64, 0.9)',
                        'rgba(255, 99, 71, 0.9)'
                    ],
                    borderColor: [
                        'rgba(255, 140, 66, 1)',
                        'rgba(255, 107, 107, 1)',
                        'rgba(255, 82, 82, 1)',
                        'rgba(255, 177, 66, 1)',
                        'rgba(255, 200, 87, 1)',
                        'rgba(252, 227, 138, 1)',
                        'rgba(254, 202, 87, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 71, 1)'
                    ],
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: true,
                plugins: {{
                    legend: {{
                        display: false
                    }},
                    title: {{
                        display: true,
                        text: '整体慢查执行次数分布（共 {overall_total} 条）',
                        font: {{
                            size: 18,
                            weight: 'bold'
                        }},
                        color: '#ff6b6b'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        ticks: {{
                            stepSize: 1,
                            font: {{
                                size: 12
                            }}
                        }},
                        title: {{
                            display: true,
                            text: '慢查数量',
                            font: {{
                                size: 14,
                                weight: 'bold'
                            }}
                        }}
                    }},
                    x: {{
                        ticks: {{
                            font: {{
                                size: 12
                            }}
                        }},
                        title: {{
                            display: true,
                            text: '执行次数档位',
                            font: {{
                                size: 14,
                                weight: 'bold'
                            }}
                        }}
                    }}
                }}
            }}
        }});
'''
    
    # 生成所有图表的JavaScript代码
    chart_id = 0
    for owner, stats in sorted_owners:
        chart_id += 1
        
        distribution_data = []
        for label in bin_labels:
            distribution_data.append(stats['distribution'].get(label, 0))
        
        html_content += f'''
        const ctx{chart_id} = document.getElementById('chart{chart_id}').getContext('2d');
        new Chart(ctx{chart_id}, {{
            type: 'bar',
            data: {{
                labels: {bin_labels},
                datasets: [{{
                    label: '慢查数量',
                    data: {distribution_data},
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: true,
                plugins: {{
                    legend: {{
                        display: false
                    }},
                    title: {{
                        display: true,
                        text: '{owner} 的执行次数分布',
                        font: {{
                            size: 16,
                            weight: 'bold'
                        }}
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        ticks: {{
                            stepSize: 1
                        }}
                    }}
                }}
            }}
        }});
'''
    
    html_content += '''
    </script>
</body>
</html>
'''
    
    # 保存HTML文件
    with open(html_filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ 已生成: {os.path.basename(html_filename)}")
    print(f"   共分析了 {len(owner_stats)} 个负责人的执行次数分布")
    
    return html_filename


def compare_with_history(current_records, current_date, history_dir, baseline_date):
    """步骤5: 与历史数据对比"""
    log_step("步骤5: 与历史数据对比分析")
    
    # 使用指定的基线日期
    latest_history_date = baseline_date
    
    # 验证基线日期目录是否存在
    history_date_dir = os.path.join(history_dir, latest_history_date)
    if not os.path.exists(history_date_dir):
        print(f"   ❌ 错误: 指定的基线日期目录不存在: {history_date_dir}")
        print(f"   可用的历史日期:")
        if os.path.exists(history_dir):
            for d in sorted(os.listdir(history_dir)):
                date_path = os.path.join(history_dir, d)
                if os.path.isdir(date_path):
                    print(f"      - {d}")
        return None
    
    history_json_path = None
    
    # 查找历史JSON文件
    for f in os.listdir(history_date_dir):
        if f.startswith('full_excel_data') and f.endswith('.json'):
            history_json_path = os.path.join(history_date_dir, f)
            break
    
    if not history_json_path:
        print(f"   ⚠️  未找到历史数据JSON文件")
        return None
    
    print(f"   对比基线: {latest_history_date}")
    
    # 读取历史数据
    with open(history_json_path, 'r', encoding='utf-8') as f:
        history_data = json.load(f)
    
    # 分析变化（区分加白和未加白）
    def analyze_by_cluster(records):
        cluster_stats = defaultdict(lambda: {'total': 0, 'whitelisted': 0, 'normal': 0})
        for record in records:
            identity = record.get('identity', '')
            cluster_id = record.get('database_cluster', '')
            cluster_name = identity if identity else cluster_id
            if cluster_name:
                is_whitelisted, _ = check_whitelist(record)
                cluster_stats[cluster_name]['total'] += 1
                if is_whitelisted:
                    cluster_stats[cluster_name]['whitelisted'] += 1
                else:
                    cluster_stats[cluster_name]['normal'] += 1
        return cluster_stats
    
    old_clusters = analyze_by_cluster(history_data['records'])
    new_clusters = analyze_by_cluster(current_records)
    
    all_cluster_names = set(old_clusters.keys()) | set(new_clusters.keys())
    cluster_changes = []
    
    for cluster in all_cluster_names:
        old_data = old_clusters.get(cluster, {'total': 0, 'whitelisted': 0, 'normal': 0})
        new_data = new_clusters.get(cluster, {'total': 0, 'whitelisted': 0, 'normal': 0})
        
        old_count = old_data['total']
        new_count = new_data['total']
        old_whitelisted = old_data['whitelisted']
        new_whitelisted = new_data['whitelisted']
        old_normal = old_data['normal']
        new_normal = new_data['normal']
        
        diff = new_count - old_count
        diff_whitelisted = new_whitelisted - old_whitelisted
        diff_normal = new_normal - old_normal
        
        if old_count > 0:
            diff_pct = (diff / old_count) * 100
        elif new_count > 0:
            diff_pct = float('inf')
        else:
            diff_pct = 0
        
        # 只看未加白的SQL来判断状态（加白的不算问题）
        status = '改善' if diff_normal < 0 else ('恶化' if diff_normal > 0 else '持平')
        if old_count == 0:
            status = '新增'
        elif new_count == 0:
            status = '已解决'
        elif old_normal > 0 and new_normal == 0:
            status = '已解决(仅剩加白)'
        
        cluster_changes.append({
            '集群名称': cluster,
            '旧数据总数': old_count,
            '旧数据加白': old_whitelisted,
            '旧数据未加白': old_normal,
            '新数据总数': new_count,
            '新数据加白': new_whitelisted,
            '新数据未加白': new_normal,
            '总变化量': diff,
            '加白变化': diff_whitelisted,
            '未加白变化': diff_normal,
            '变化率(%)': round(diff_pct, 1) if diff_pct != float('inf') else 'N/A',
            '状态': status
        })
    
    cluster_changes.sort(key=lambda x: x['未加白变化'])
    
    # 保存对比报告到对比分析目录
    compare_dir = os.path.join('对比分析')
    os.makedirs(compare_dir, exist_ok=True)
    
    # 保存详细的CSV对比数据
    compare_filename = os.path.join(compare_dir, f'对比分析_{latest_history_date}_vs_{current_date}.csv')
    compare_df = pd.DataFrame(cluster_changes)
    compare_df.to_csv(compare_filename, index=False, encoding='utf-8-sig')
    
    print(f"✅ 已生成对比明细CSV: {compare_filename}")
    
    # 统计摘要
    old_total = len(history_data['records'])
    new_total = len(current_records)
    diff_total = new_total - old_total
    
    print(f"\n   总体变化:")
    print(f"   {latest_history_date}: {old_total:,} 条")
    print(f"   {current_date}: {new_total:,} 条")
    print(f"   变化: {diff_total:+,} 条 ({(diff_total/old_total*100):+.1f}%)")
    
    if diff_total < 0:
        print(f"   ✅ 改善！减少了 {abs(diff_total):,} 条慢查询")
    elif diff_total > 0:
        print(f"   ⚠️  恶化！增加了 {diff_total:,} 条慢查询")
    
    # 生成总体汇总报告
    generate_summary_report(cluster_changes, old_total, new_total, 
                           latest_history_date, current_date, compare_dir)
    
    return cluster_changes


def generate_summary_report(cluster_changes, old_total, new_total, 
                           baseline_date, current_date, compare_dir):
    """生成总体汇总报告"""
    
    # 分类统计
    improved = [c for c in cluster_changes if c['状态'] == '改善']
    worsened = [c for c in cluster_changes if c['状态'] == '恶化']
    resolved = [c for c in cluster_changes if c['状态'] == '已解决']
    resolved_wl = [c for c in cluster_changes if c['状态'] == '已解决(仅剩加白)']
    new_added = [c for c in cluster_changes if c['状态'] == '新增']
    unchanged = [c for c in cluster_changes if c['状态'] == '持平']
    
    diff_total = new_total - old_total
    diff_pct = (diff_total / old_total) * 100 if old_total > 0 else 0
    
    # 计算加白和未加白的总数
    old_total_whitelisted = sum(c['旧数据加白'] for c in cluster_changes)
    new_total_whitelisted = sum(c['新数据加白'] for c in cluster_changes)
    old_total_normal = sum(c['旧数据未加白'] for c in cluster_changes)
    new_total_normal = sum(c['新数据未加白'] for c in cluster_changes)
    
    diff_whitelisted = new_total_whitelisted - old_total_whitelisted
    diff_normal = new_total_normal - old_total_normal
    
    total_improved = sum(abs(c['未加白变化']) for c in improved)
    total_worsened = sum(c['未加白变化'] for c in worsened)
    total_resolved = sum(c['旧数据未加白'] for c in resolved)
    total_resolved_wl = sum(c['旧数据未加白'] for c in resolved_wl)
    total_new = sum(c['新数据未加白'] for c in new_added)
    
    # 生成Markdown汇总报告
    summary_filename = os.path.join(compare_dir, f'优化总结报告_{baseline_date}_vs_{current_date}.md')
    
    with open(summary_filename, 'w', encoding='utf-8') as f:
        f.write(f'# 慢查询优化总结报告\n\n')
        f.write(f'**对比时间段**: {baseline_date} → {current_date}\n\n')
        f.write(f'**生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n')
        
        # 总体概况
        f.write(f'## 📊 总体概况\n\n')
        f.write(f'### 慢查询总数统计\n\n')
        f.write(f'| 指标 | {baseline_date} | {current_date} | 变化 | 占比 |\n')
        f.write(f'|------|----------------|----------------|------|------|\n')
        f.write(f'| 慢查询总数 | {old_total:,} 条 | {new_total:,} 条 | {diff_total:+,} 条 | {diff_pct:+.1f}% |\n')
        f.write(f'| └─ 未加白SQL | {old_total_normal:,} 条 | {new_total_normal:,} 条 | {diff_normal:+,} 条 | {(diff_normal/old_total_normal*100 if old_total_normal > 0 else 0):+.1f}% |\n')
        f.write(f'| └─ 加白SQL | {old_total_whitelisted:,} 条 | {new_total_whitelisted:,} 条 | {diff_whitelisted:+,} 条 | {(diff_whitelisted/old_total_whitelisted*100 if old_total_whitelisted > 0 else 0):+.1f}% |\n')
        f.write(f'| 涉及集群数 | {len([c for c in cluster_changes if c["旧数据总数"] > 0])} 个 | ')
        f.write(f'{len([c for c in cluster_changes if c["新数据总数"] > 0])} 个 | ')
        f.write(f'{len([c for c in cluster_changes if c["新数据总数"] > 0]) - len([c for c in cluster_changes if c["旧数据总数"] > 0]):+d} 个 | - |\n\n')
        
        f.write(f'> **说明**: 加白SQL指测试环境等已经标记为可忽略的慢查询，未加白SQL才是真正需要治理的问题SQL\n\n')
        
        # 优化评价（基于未加白SQL）
        if diff_normal < 0:
            f.write(f'### ✅ 优化效果：**显著改善**\n\n')
            f.write(f'相比 {baseline_date}，**未加白的慢查询**减少了 **{abs(diff_normal):,} 条**，优化率达到 **{abs(diff_normal/old_total_normal*100 if old_total_normal > 0 else 0):.1f}%**\n\n')
            if diff_total >= 0:
                f.write(f'> 注意：虽然总数增加了 {diff_total:,} 条，但主要是加白SQL增加了 {diff_whitelisted:,} 条，实际问题SQL在减少\n\n')
        elif diff_normal > 0:
            f.write(f'### ⚠️ 优化效果：**有所恶化**\n\n')
            f.write(f'相比 {baseline_date}，**未加白的慢查询**增加了 **{diff_normal:,} 条**，恶化率为 **{diff_normal/old_total_normal*100 if old_total_normal > 0 else 0:.1f}%**\n\n')
        else:
            f.write(f'### ➡️ 优化效果：**保持稳定**\n\n')
            f.write(f'未加白的慢查询数量保持不变\n\n')
        
        # 分类统计
        f.write(f'## 📈 分类统计（基于未加白SQL）\n\n')
        f.write(f'| 类别 | 集群数 | 未加白SQL变化 | 说明 |\n')
        f.write(f'|------|--------|--------------|------|\n')
        f.write(f'| ✅ 改善 | {len(improved)} 个 | 减少 {total_improved:,} 条 | 未加白慢查询减少的集群 |\n')
        f.write(f'| 🎉 完全解决 | {len(resolved)} 个 | 减少 {total_resolved:,} 条 | 慢查询完全消除的集群 |\n')
        f.write(f'| 🎉 仅剩加白 | {len(resolved_wl)} 个 | 减少 {total_resolved_wl:,} 条 | 未加白SQL已全部解决 |\n')
        f.write(f'| ⚠️ 恶化 | {len(worsened)} 个 | 增加 {total_worsened:,} 条 | 未加白慢查询增加的集群 |\n')
        f.write(f'| 🆕 新增 | {len(new_added)} 个 | 新增 {total_new:,} 条 | 新出现慢查询的集群 |\n')
        f.write(f'| ➡️ 持平 | {len(unchanged)} 个 | 无变化 | 未加白慢查询数量不变 |\n\n')
        
        # Top改善集群
        if improved:
            f.write(f'## 🏆 Top 10 改善最显著的集群\n\n')
            f.write(f'| 排名 | 集群名称 | 旧数据(总/未加白) | 新数据(总/未加白) | 未加白减少量 | 改善率 |\n')
            f.write(f'|------|----------|------------------|------------------|--------------|--------|\n')
            for i, c in enumerate(improved[:10], 1):
                old_normal_pct = (abs(c['未加白变化']) / c['旧数据未加白'] * 100) if c['旧数据未加白'] > 0 else 0
                f.write(f'| {i} | {c["集群名称"]} | {c["旧数据总数"]:,}/{c["旧数据未加白"]:,} | {c["新数据总数"]:,}/{c["新数据未加白"]:,} | ')
                f.write(f'{abs(c["未加白变化"]):,} | {old_normal_pct:.1f}% |\n')
            f.write(f'\n')
        
        # 完全解决的集群
        if resolved:
            f.write(f'## 🎉 完全解决的集群（{len(resolved)}个）\n\n')
            f.write(f'以下集群的慢查询已完全消除：\n\n')
            f.write(f'| 集群名称 | 原有总数 | 原有未加白 |\n')
            f.write(f'|----------|----------|------------|\n')
            for c in sorted(resolved, key=lambda x: x['旧数据总数'], reverse=True):
                f.write(f'| {c["集群名称"]} | {c["旧数据总数"]:,} 条 | {c["旧数据未加白"]:,} 条 |\n')
            f.write(f'\n')
        
        # 仅剩加白的集群
        if resolved_wl:
            f.write(f'## 🎉 未加白SQL已完全解决的集群（{len(resolved_wl)}个）\n\n')
            f.write(f'以下集群的未加白慢查询已完全消除，仅剩加白SQL：\n\n')
            f.write(f'| 集群名称 | 原有未加白 | 当前加白SQL |\n')
            f.write(f'|----------|------------|-------------|\n')
            for c in sorted(resolved_wl, key=lambda x: x['旧数据未加白'], reverse=True):
                f.write(f'| {c["集群名称"]} | {c["旧数据未加白"]:,} 条 | {c["新数据加白"]:,} 条 |\n')
            f.write(f'\n')
        
        # 需要关注的恶化集群
        if worsened:
            f.write(f'## ⚠️ 需要关注的恶化集群\n\n')
            f.write(f'| 排名 | 集群名称 | 旧数据(总/未加白) | 新数据(总/未加白) | 未加白增加量 | 恶化率 |\n')
            f.write(f'|------|----------|------------------|------------------|--------------|--------|\n')
            worsened_sorted = sorted(worsened, key=lambda x: x['未加白变化'], reverse=True)
            for i, c in enumerate(worsened_sorted[:10], 1):
                rate_pct = (c['未加白变化'] / c['旧数据未加白'] * 100) if c['旧数据未加白'] > 0 else 0
                f.write(f'| {i} | {c["集群名称"]} | {c["旧数据总数"]:,}/{c["旧数据未加白"]:,} | {c["新数据总数"]:,}/{c["新数据未加白"]:,} | ')
                f.write(f'{c["未加白变化"]:,} | +{rate_pct:.1f}% |\n')
            f.write(f'\n')
        
        # 新增集群
        if new_added:
            f.write(f'## 🆕 新增的集群（{len(new_added)}个）\n\n')
            f.write(f'| 集群名称 | 总数 | 未加白 | 加白 |\n')
            f.write(f'|----------|------|--------|------|\n')
            for c in sorted(new_added, key=lambda x: x['新数据未加白'], reverse=True):
                f.write(f'| {c["集群名称"]} | {c["新数据总数"]:,} 条 | {c["新数据未加白"]:,} 条 | {c["新数据加白"]:,} 条 |\n')
            f.write(f'\n')
        
        # 优化建议
        f.write(f'## 💡 优化建议\n\n')
        if diff_normal < 0:
            f.write(f'1. **继续保持优化势头**\n')
            f.write(f'   - 未加白SQL减少了 {abs(diff_normal):,} 条，优化效果显著\n')
            f.write(f'   - 重点关注Top改善集群的优化经验，推广到其他集群\n\n')
        
        if worsened:
            f.write(f'2. **重点关注恶化集群**\n')
            f.write(f'   - 共有 {len(worsened)} 个集群的未加白SQL出现恶化，需要及时介入\n')
            f.write(f'   - 优先处理恶化最严重的前5个集群\n\n')
        
        if new_added:
            new_normal_total = sum(c['新数据未加白'] for c in new_added)
            f.write(f'3. **跟进新增集群**\n')
            f.write(f'   - {len(new_added)} 个集群新出现慢查询（未加白: {new_normal_total:,} 条）\n')
            f.write(f'   - 建议排查新增原因，避免问题扩散\n\n')
        
        if resolved_wl:
            f.write(f'4. **关注仅剩加白SQL的集群**\n')
            f.write(f'   - {len(resolved_wl)} 个集群的未加白SQL已全部解决，仅剩加白SQL\n')
            f.write(f'   - 建议评估这些加白SQL是否可以进一步优化或下线\n\n')
        
        f.write(f'## 📝 详细数据\n\n')
        f.write(f'- **详细对比数据**: 对比分析_{baseline_date}_vs_{current_date}.csv\n')
        f.write(f'- **当前数据**: 历史报告/{current_date}/\n')
        f.write(f'- **基线数据**: 历史报告/{baseline_date}/\n')
    
    print(f"✅ 已生成优化总结报告: {summary_filename}")


def organize_files(excel_file, date_folder):
    """步骤6: 整理文件到日期目录"""
    log_step("步骤6: 整理文件到日期目录")
    
    # 移动Excel文件
    if os.path.exists(excel_file):
        dst = os.path.join(date_folder, os.path.basename(excel_file))
        if os.path.abspath(excel_file) != os.path.abspath(dst):
            shutil.move(excel_file, dst)
            print(f"   ✅ 移动Excel文件到日期目录")
    
    print(f"   ✅ 所有文件已整理完成")


def main():
    """主函数"""
    
    print("\n" + "="*70)
    print("🚀 慢查询完整分析流程自动化工具")
    print("="*70)
    
    if len(sys.argv) < 3:
        print("\n❌ 错误: 缺少必要参数")
        print("\n用法:")
        print(f"   python3 {sys.argv[0]} <Excel文件名> <对比基线日期>")
        print("\n参数说明:")
        print(f"   Excel文件名: 慢查询Excel文件")
        print(f"   对比基线日期: 格式YYYY-MM-DD，用于对比的历史数据日期（必填）")
        print("\n示例:")
        print(f"   python3 {sys.argv[0]} idp_export_idp_23817816_企业应用慢查支持.xlsx 2025-09-09")
        
        # 显示可用的历史日期
        history_dir = "历史报告"
        if os.path.exists(history_dir):
            print("\n可用的历史日期:")
            for d in sorted(os.listdir(history_dir)):
                date_path = os.path.join(history_dir, d)
                if os.path.isdir(date_path):
                    print(f"   - {d}")
        
        sys.exit(1)
    
    excel_file = sys.argv[1]
    baseline_date = sys.argv[2]
    current_date = datetime.now().strftime("%Y-%m-%d")
    
    # 验证日期格式
    try:
        datetime.strptime(baseline_date, "%Y-%m-%d")
    except ValueError:
        print(f"\n❌ 错误: 日期格式不正确: {baseline_date}")
        print(f"   正确格式: YYYY-MM-DD (例如: 2025-09-09)")
        sys.exit(1)
    
    # 创建日期目录
    history_dir = "历史报告"
    date_folder = os.path.join(history_dir, current_date)
    os.makedirs(date_folder, exist_ok=True)
    
    # 执行分析流程
    result = extract_excel_data(excel_file)
    if result is None:
        sys.exit(1)
    
    records, source_file = result
    
    json_file = save_json_data(records, source_file, date_folder)
    
    top_clusters = generate_cluster_analysis(records, date_folder)
    
    top_owners = generate_owner_analysis(records, date_folder)
    
    # 新增功能：生成负责人慢查执行次数分布分析
    generate_distribution_analysis(records, date_folder)
    
    compare_with_history(records, current_date, history_dir, baseline_date)
    
    organize_files(excel_file, date_folder)
    
    # 最终总结
    log_step("✅ 分析完成！")
    
    print(f"\n📁 生成的文件位置: {date_folder}/")
    print(f"\n📊 分析摘要:")
    print(f"   - 总慢查询数: {len(records):,} 条")
    print(f"   - 涉及负责人: {len(top_owners)} 人")
    print(f"   - Top 10 集群:")
    for i, (cluster_name, stats) in enumerate(top_clusters, 1):
        print(f"      {i:2d}. {cluster_name}: {stats['count']:,} 条")
    
    print(f"\n" + "="*70 + "\n")


if __name__ == "__main__":
    main()

