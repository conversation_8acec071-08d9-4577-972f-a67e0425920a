# 慢查询优化总结报告

**对比时间段**: 2025-09-09 → 2025-10-22

**生成时间**: 2025-10-22 12:04:42

## 📊 总体概况

### 慢查询总数统计

| 指标 | 2025-09-09 | 2025-10-22 | 变化 | 占比 |
|------|----------------|----------------|------|------|
| 慢查询总数 | 9,195 条 | 7,653 条 | -1,542 条 | -16.8% |
| └─ 未加白SQL | 6,916 条 | 5,569 条 | -1,347 条 | -19.5% |
| └─ 加白SQL | 2,279 条 | 2,084 条 | -195 条 | -8.6% |
| 涉及集群数 | 79 个 | 75 个 | -4 个 | - |

> **说明**: 加白SQL指测试环境等已经标记为可忽略的慢查询，未加白SQL才是真正需要治理的问题SQL

### ✅ 优化效果：**显著改善**

相比 2025-09-09，**未加白的慢查询**减少了 **1,347 条**，优化率达到 **19.5%**

## 📈 分类统计（基于未加白SQL）

| 类别 | 集群数 | 未加白SQL变化 | 说明 |
|------|--------|--------------|------|
| ✅ 改善 | 39 个 | 减少 1,658 条 | 未加白慢查询减少的集群 |
| 🎉 完全解决 | 11 个 | 减少 20 条 | 慢查询完全消除的集群 |
| 🎉 仅剩加白 | 0 个 | 减少 0 条 | 未加白SQL已全部解决 |
| ⚠️ 恶化 | 13 个 | 增加 246 条 | 未加白慢查询增加的集群 |
| 🆕 新增 | 7 个 | 新增 85 条 | 新出现慢查询的集群 |
| ➡️ 持平 | 16 个 | 无变化 | 未加白慢查询数量不变 |

## 🏆 Top 10 改善最显著的集群

| 排名 | 集群名称 | 旧数据(总/未加白) | 新数据(总/未加白) | 未加白减少量 | 改善率 |
|------|----------|------------------|------------------|--------------|--------|
| 1 | eeDataWareHourse | 1,463/1,463 | 941/941 | 522 | 35.7% |
| 2 | hrdpKdb | 925/925 | 691/691 | 234 | 25.3% |
| 3 | eeDataIsHrds | 1,670/1,670 | 1,453/1,453 | 217 | 13.0% |
| 4 | xzWeide57 | 287/287 | 137/137 | 150 | 52.3% |
| 5 | isRecruit | 262/261 | 159/159 | 102 | 39.1% |
| 6 | finBra | 169/169 | 86/86 | 83 | 49.1% |
| 7 | eeDataAdminKdb | 108/108 | 62/62 | 46 | 42.6% |
| 8 | eeKaleidoWorkspace | 41/41 | 1/1 | 40 | 97.6% |
| 9 | KaleidoForSYH | 56/56 | 20/20 | 36 | 64.3% |
| 10 | eaSchoolPre | 33/33 | 6/6 | 27 | 81.8% |

## 🎉 完全解决的集群（11个）

以下集群的慢查询已完全消除：

| 集群名称 | 原有总数 | 原有未加白 |
|----------|----------|------------|
| eeIhrAi | 13 条 | 2 条 |
| eeGeneralDict | 4 条 | 4 条 |
| eSignTs | 4 条 | 4 条 |
| manpower | 3 条 | 3 条 |
| recruitAbility | 1 条 | 1 条 |
| fulcrumEngineProduction | 1 条 | 1 条 |
| procEnterpriseInfoPlatform | 1 条 | 1 条 |
| magicAi | 1 条 | 1 条 |
| aiReportPro | 1 条 | 1 条 |
| msgms | 1 条 | 1 条 |
| eaDataAssetPreonline | 1 条 | 1 条 |

## ⚠️ 需要关注的恶化集群

| 排名 | 集群名称 | 旧数据(总/未加白) | 新数据(总/未加白) | 未加白增加量 | 恶化率 |
|------|----------|------------------|------------------|--------------|--------|
| 1 | finHumProd | 46/46 | 167/166 | 120 | +260.9% |
| 2 | finKarms | 104/104 | 139/139 | 35 | +33.7% |
| 3 | finForecast | 8/8 | 30/30 | 22 | +275.0% |
| 4 | ihrCbBudget | 11/11 | 29/29 | 18 | +163.6% |
| 5 | finManagmentReport | 6/6 | 18/18 | 12 | +200.0% |
| 6 | eaLegal | 43/43 | 51/51 | 8 | +18.6% |
| 7 | fulcrumBizDataStorageProdPart1 | 117/117 | 124/124 | 7 | +6.0% |
| 8 | ktmsRiskPro | 1/1 | 7/7 | 6 | +600.0% |
| 9 | ktmsOverseas | 22/22 | 28/28 | 6 | +27.3% |
| 10 | fsscPro | 3/3 | 7/7 | 4 | +133.3% |

## 🆕 新增的集群（7个）

| 集群名称 | 总数 | 未加白 | 加白 |
|----------|------|--------|------|
| finKtmsInternetIpr | 66 条 | 66 条 | 0 条 |
| CloudMirror | 7 条 | 7 条 | 0 条 |
| legalDssFdd | 4 条 | 4 条 | 0 条 |
| eaDataAsset | 3 条 | 3 条 | 0 条 |
| eeIncentiveUat | 2 条 | 2 条 | 0 条 |
| kaleidoForFIN | 2 条 | 2 条 | 0 条 |
| procSupplierPlatform | 1 条 | 1 条 | 0 条 |

## 💡 优化建议

1. **继续保持优化势头**
   - 未加白SQL减少了 1,347 条，优化效果显著
   - 重点关注Top改善集群的优化经验，推广到其他集群

2. **重点关注恶化集群**
   - 共有 13 个集群的未加白SQL出现恶化，需要及时介入
   - 优先处理恶化最严重的前5个集群

3. **跟进新增集群**
   - 7 个集群新出现慢查询（未加白: 85 条）
   - 建议排查新增原因，避免问题扩散

## 📝 详细数据

- **详细对比数据**: 对比分析_2025-09-09_vs_2025-10-22.csv
- **当前数据**: 历史报告/2025-10-22/
- **基线数据**: 历史报告/2025-09-09/
