<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>负责人慢查执行次数分布分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Microsoft YaHei", sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        .header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .summary-card .value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        .summary-card .label {
            font-size: 14px;
            color: #666;
        }
        .content {
            padding: 30px;
        }
        .owner-section {
            margin-bottom: 40px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .owner-header {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .owner-header .count {
            font-size: 14px;
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
        }
        .chart-container {
            padding: 20px;
            background: #fafafa;
        }
        canvas {
            max-height: 300px;
        }
        .distribution-table {
            width: 100%;
            margin-top: 20px;
            border-collapse: collapse;
        }
        .distribution-table th,
        .distribution-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        .distribution-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .distribution-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .distribution-table tr:hover {
            background: #e8eaf6;
        }
        .percentage {
            color: #666;
            font-size: 12px;
        }
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 负责人慢查执行次数分布分析</h1>
            <div class="subtitle">生成时间: 2025-10-22 12:04:42</div>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <div class="label">负责人总数</div>
                <div class="value">27</div>
            </div>
            <div class="summary-card">
                <div class="label">慢查总数（未加白）</div>
                <div class="value">5569</div>
            </div>
            <div class="summary-card">
                <div class="label">平均每人慢查数</div>
                <div class="value">206</div>
            </div>
        </div>
        
        <div class="content">
            <!-- 整体分布图表 -->
            <div class="owner-section" style="background: linear-gradient(135deg, #f6d365 0%, #fda085 100%); border: 2px solid #ff8c42;">
                <div class="owner-header" style="background: linear-gradient(90deg, #ff8c42 0%, #ff6b6b 100%);">
                    <span>🌟 整体执行次数分布（所有负责人汇总）</span>
                    <span class="count">总计: 5569 条慢查</span>
                </div>
                <div class="chart-container" style="background: white;">
                    <canvas id="chartOverall"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>3421<br><span class="percentage">(61.4%)</span></td><td>909<br><span class="percentage">(16.3%)</span></td><td>350<br><span class="percentage">(6.3%)</span></td><td>424<br><span class="percentage">(7.6%)</span></td><td>71<br><span class="percentage">(1.3%)</span></td><td>67<br><span class="percentage">(1.2%)</span></td><td>17<br><span class="percentage">(0.3%)</span></td><td>20<br><span class="percentage">(0.4%)</span></td><td>27<br><span class="percentage">(0.5%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 分隔线 -->
            <div style="margin: 40px 0; text-align: center; color: #999; font-size: 20px; font-weight: bold;">
                ━━━━━━━━━━━━━━━━━━ 各负责人明细分布 ━━━━━━━━━━━━━━━━━━
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>luzhaohan</span>
                    <span class="count">慢查总数: 3669</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart1"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>2551<br><span class="percentage">(69.5%)</span></td><td>549<br><span class="percentage">(15.0%)</span></td><td>178<br><span class="percentage">(4.9%)</span></td><td>172<br><span class="percentage">(4.7%)</span></td><td>11<br><span class="percentage">(0.3%)</span></td><td>20<br><span class="percentage">(0.5%)</span></td><td>5<br><span class="percentage">(0.1%)</span></td><td>3<br><span class="percentage">(0.1%)</span></td><td>4<br><span class="percentage">(0.1%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>gaonengquan</span>
                    <span class="count">慢查总数: 305</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart2"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>142<br><span class="percentage">(46.6%)</span></td><td>55<br><span class="percentage">(18.0%)</span></td><td>28<br><span class="percentage">(9.2%)</span></td><td>30<br><span class="percentage">(9.8%)</span></td><td>6<br><span class="percentage">(2.0%)</span></td><td>6<br><span class="percentage">(2.0%)</span></td><td>2<br><span class="percentage">(0.7%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(0.3%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>gaowen03</span>
                    <span class="count">慢查总数: 175</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart3"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>90<br><span class="percentage">(51.4%)</span></td><td>35<br><span class="percentage">(20.0%)</span></td><td>12<br><span class="percentage">(6.9%)</span></td><td>24<br><span class="percentage">(13.7%)</span></td><td>6<br><span class="percentage">(3.4%)</span></td><td>1<br><span class="percentage">(0.6%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>2<br><span class="percentage">(1.1%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>niesong</span>
                    <span class="count">慢查总数: 171</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart4"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>90<br><span class="percentage">(52.6%)</span></td><td>26<br><span class="percentage">(15.2%)</span></td><td>14<br><span class="percentage">(8.2%)</span></td><td>29<br><span class="percentage">(17.0%)</span></td><td>3<br><span class="percentage">(1.8%)</span></td><td>4<br><span class="percentage">(2.3%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(0.6%)</span></td><td>3<br><span class="percentage">(1.8%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>chenyan05</span>
                    <span class="count">慢查总数: 162</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart5"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>70<br><span class="percentage">(43.2%)</span></td><td>32<br><span class="percentage">(19.8%)</span></td><td>16<br><span class="percentage">(9.9%)</span></td><td>20<br><span class="percentage">(12.3%)</span></td><td>6<br><span class="percentage">(3.7%)</span></td><td>5<br><span class="percentage">(3.1%)</span></td><td>3<br><span class="percentage">(1.9%)</span></td><td>1<br><span class="percentage">(0.6%)</span></td><td>1<br><span class="percentage">(0.6%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>mazhijie</span>
                    <span class="count">慢查总数: 153</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart6"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>59<br><span class="percentage">(38.6%)</span></td><td>34<br><span class="percentage">(22.2%)</span></td><td>19<br><span class="percentage">(12.4%)</span></td><td>17<br><span class="percentage">(11.1%)</span></td><td>4<br><span class="percentage">(2.6%)</span></td><td>7<br><span class="percentage">(4.6%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>4<br><span class="percentage">(2.6%)</span></td><td>4<br><span class="percentage">(2.6%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>chengkun03</span>
                    <span class="count">慢查总数: 140</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart7"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>85<br><span class="percentage">(60.7%)</span></td><td>32<br><span class="percentage">(22.9%)</span></td><td>11<br><span class="percentage">(7.9%)</span></td><td>9<br><span class="percentage">(6.4%)</span></td><td>3<br><span class="percentage">(2.1%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>guoweichao</span>
                    <span class="count">慢查总数: 138</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart8"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>73<br><span class="percentage">(52.9%)</span></td><td>18<br><span class="percentage">(13.0%)</span></td><td>9<br><span class="percentage">(6.5%)</span></td><td>14<br><span class="percentage">(10.1%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>2<br><span class="percentage">(1.4%)</span></td><td>1<br><span class="percentage">(0.7%)</span></td><td>1<br><span class="percentage">(0.7%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>liulei07</span>
                    <span class="count">慢查总数: 128</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart9"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>33<br><span class="percentage">(25.8%)</span></td><td>24<br><span class="percentage">(18.8%)</span></td><td>14<br><span class="percentage">(10.9%)</span></td><td>32<br><span class="percentage">(25.0%)</span></td><td>12<br><span class="percentage">(9.4%)</span></td><td>4<br><span class="percentage">(3.1%)</span></td><td>4<br><span class="percentage">(3.1%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>2<br><span class="percentage">(1.6%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>liuzhaokao</span>
                    <span class="count">慢查总数: 84</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart10"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>34<br><span class="percentage">(40.5%)</span></td><td>12<br><span class="percentage">(14.3%)</span></td><td>9<br><span class="percentage">(10.7%)</span></td><td>14<br><span class="percentage">(16.7%)</span></td><td>6<br><span class="percentage">(7.1%)</span></td><td>7<br><span class="percentage">(8.3%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(1.2%)</span></td><td>1<br><span class="percentage">(1.2%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>qinyunfei</span>
                    <span class="count">慢查总数: 82</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart11"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>39<br><span class="percentage">(47.6%)</span></td><td>18<br><span class="percentage">(22.0%)</span></td><td>6<br><span class="percentage">(7.3%)</span></td><td>9<br><span class="percentage">(11.0%)</span></td><td>1<br><span class="percentage">(1.2%)</span></td><td>1<br><span class="percentage">(1.2%)</span></td><td>1<br><span class="percentage">(1.2%)</span></td><td>3<br><span class="percentage">(3.7%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>wangjianfei</span>
                    <span class="count">慢查总数: 81</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart12"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>29<br><span class="percentage">(35.8%)</span></td><td>16<br><span class="percentage">(19.8%)</span></td><td>12<br><span class="percentage">(14.8%)</span></td><td>12<br><span class="percentage">(14.8%)</span></td><td>3<br><span class="percentage">(3.7%)</span></td><td>3<br><span class="percentage">(3.7%)</span></td><td>1<br><span class="percentage">(1.2%)</span></td><td>2<br><span class="percentage">(2.5%)</span></td><td>3<br><span class="percentage">(3.7%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>zhaochenhui</span>
                    <span class="count">慢查总数: 58</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart13"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>33<br><span class="percentage">(56.9%)</span></td><td>6<br><span class="percentage">(10.3%)</span></td><td>3<br><span class="percentage">(5.2%)</span></td><td>8<br><span class="percentage">(13.8%)</span></td><td>2<br><span class="percentage">(3.4%)</span></td><td>3<br><span class="percentage">(5.2%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(1.7%)</span></td><td>2<br><span class="percentage">(3.4%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>guoyanlong</span>
                    <span class="count">慢查总数: 41</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart14"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>20<br><span class="percentage">(48.8%)</span></td><td>7<br><span class="percentage">(17.1%)</span></td><td>9<br><span class="percentage">(22.0%)</span></td><td>3<br><span class="percentage">(7.3%)</span></td><td>2<br><span class="percentage">(4.9%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>wangye07</span>
                    <span class="count">慢查总数: 38</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart15"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>17<br><span class="percentage">(44.7%)</span></td><td>12<br><span class="percentage">(31.6%)</span></td><td>2<br><span class="percentage">(5.3%)</span></td><td>2<br><span class="percentage">(5.3%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>shiyu</span>
                    <span class="count">慢查总数: 29</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart16"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>15<br><span class="percentage">(51.7%)</span></td><td>6<br><span class="percentage">(20.7%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>5<br><span class="percentage">(17.2%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(3.4%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>2<br><span class="percentage">(6.9%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>liruisheng</span>
                    <span class="count">慢查总数: 26</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart17"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>5<br><span class="percentage">(19.2%)</span></td><td>6<br><span class="percentage">(23.1%)</span></td><td>1<br><span class="percentage">(3.8%)</span></td><td>6<br><span class="percentage">(23.1%)</span></td><td>4<br><span class="percentage">(15.4%)</span></td><td>2<br><span class="percentage">(7.7%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>2<br><span class="percentage">(7.7%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>wb_louhao</span>
                    <span class="count">慢查总数: 25</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart18"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>12<br><span class="percentage">(48.0%)</span></td><td>9<br><span class="percentage">(36.0%)</span></td><td>3<br><span class="percentage">(12.0%)</span></td><td>1<br><span class="percentage">(4.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>liuyouchao</span>
                    <span class="count">慢查总数: 19</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart19"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>6<br><span class="percentage">(31.6%)</span></td><td>3<br><span class="percentage">(15.8%)</span></td><td>1<br><span class="percentage">(5.3%)</span></td><td>8<br><span class="percentage">(42.1%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(5.3%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>zouweicheng</span>
                    <span class="count">慢查总数: 18</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart20"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>4<br><span class="percentage">(22.2%)</span></td><td>8<br><span class="percentage">(44.4%)</span></td><td>2<br><span class="percentage">(11.1%)</span></td><td>4<br><span class="percentage">(22.2%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>wangjun07</span>
                    <span class="count">慢查总数: 16</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart21"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>7<br><span class="percentage">(43.8%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(6.2%)</span></td><td>4<br><span class="percentage">(25.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(6.2%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>2<br><span class="percentage">(12.5%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>wb_luoyang05</span>
                    <span class="count">慢查总数: 6</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart22"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>5<br><span class="percentage">(83.3%)</span></td><td>1<br><span class="percentage">(16.7%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>yechao</span>
                    <span class="count">慢查总数: 1</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart23"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>1<br><span class="percentage">(100.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>wb_zhanghongyu08</span>
                    <span class="count">慢查总数: 1</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart24"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>1<br><span class="percentage">(100.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>fanlvlun</span>
                    <span class="count">慢查总数: 1</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart25"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(100.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>jiqiang</span>
                    <span class="count">慢查总数: 1</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart26"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(100.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="owner-section">
                <div class="owner-header">
                    <span>zangshaoqi</span>
                    <span class="count">慢查总数: 1</span>
                </div>
                <div class="chart-container">
                    <canvas id="chart27"></canvas>
                    <table class="distribution-table">
                        <thead>
                            <tr>
                                <th>执行次数档位</th>
<th>≤2次</th><th>3-5次</th><th>6-10次</th><th>11-50次</th><th>51-100次</th><th>101-300次</th><th>301-500次</th><th>501-1000次</th><th>>1000次</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>慢查数量</strong></td>
<td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>1<br><span class="percentage">(100.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td><td>0<br><span class="percentage">(0.0%)</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
        
        <div class="footer">
            © 2025 慢SQL治理分析系统 | 本报告用于内部优化决策参考
        </div>
    </div>
    
    <script>

        // 整体分布图表
        const ctxOverall = document.getElementById('chartOverall').getContext('2d');
        new Chart(ctxOverall, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [3421, 909, 350, 424, 71, 67, 17, 20, 27],
                    backgroundColor: [
                        'rgba(255, 140, 66, 0.9)',
                        'rgba(255, 107, 107, 0.9)',
                        'rgba(255, 82, 82, 0.9)',
                        'rgba(255, 177, 66, 0.9)',
                        'rgba(255, 200, 87, 0.9)',
                        'rgba(252, 227, 138, 0.9)',
                        'rgba(254, 202, 87, 0.9)',
                        'rgba(255, 159, 64, 0.9)',
                        'rgba(255, 99, 71, 0.9)'
                    ],
                    borderColor: [
                        'rgba(255, 140, 66, 1)',
                        'rgba(255, 107, 107, 1)',
                        'rgba(255, 82, 82, 1)',
                        'rgba(255, 177, 66, 1)',
                        'rgba(255, 200, 87, 1)',
                        'rgba(252, 227, 138, 1)',
                        'rgba(254, 202, 87, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 71, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '整体慢查执行次数分布（共 5569 条）',
                        font: {
                            size: 18,
                            weight: 'bold'
                        },
                        color: '#ff6b6b'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            font: {
                                size: 12
                            }
                        },
                        title: {
                            display: true,
                            text: '慢查数量',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 12
                            }
                        },
                        title: {
                            display: true,
                            text: '执行次数档位',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        }
                    }
                }
            }
        });

        const ctx1 = document.getElementById('chart1').getContext('2d');
        new Chart(ctx1, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [2551, 549, 178, 172, 11, 20, 5, 3, 4],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'luzhaohan 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx2 = document.getElementById('chart2').getContext('2d');
        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [142, 55, 28, 30, 6, 6, 2, 0, 1],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'gaonengquan 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx3 = document.getElementById('chart3').getContext('2d');
        new Chart(ctx3, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [90, 35, 12, 24, 6, 1, 0, 0, 2],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'gaowen03 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx4 = document.getElementById('chart4').getContext('2d');
        new Chart(ctx4, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [90, 26, 14, 29, 3, 4, 0, 1, 3],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'niesong 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx5 = document.getElementById('chart5').getContext('2d');
        new Chart(ctx5, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [70, 32, 16, 20, 6, 5, 3, 1, 1],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'chenyan05 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx6 = document.getElementById('chart6').getContext('2d');
        new Chart(ctx6, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [59, 34, 19, 17, 4, 7, 0, 4, 4],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'mazhijie 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx7 = document.getElementById('chart7').getContext('2d');
        new Chart(ctx7, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [85, 32, 11, 9, 3, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'chengkun03 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx8 = document.getElementById('chart8').getContext('2d');
        new Chart(ctx8, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [73, 18, 9, 14, 0, 2, 1, 1, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'guoweichao 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx9 = document.getElementById('chart9').getContext('2d');
        new Chart(ctx9, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [33, 24, 14, 32, 12, 4, 4, 0, 2],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'liulei07 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx10 = document.getElementById('chart10').getContext('2d');
        new Chart(ctx10, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [34, 12, 9, 14, 6, 7, 0, 1, 1],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'liuzhaokao 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx11 = document.getElementById('chart11').getContext('2d');
        new Chart(ctx11, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [39, 18, 6, 9, 1, 1, 1, 3, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'qinyunfei 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx12 = document.getElementById('chart12').getContext('2d');
        new Chart(ctx12, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [29, 16, 12, 12, 3, 3, 1, 2, 3],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'wangjianfei 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx13 = document.getElementById('chart13').getContext('2d');
        new Chart(ctx13, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [33, 6, 3, 8, 2, 3, 0, 1, 2],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'zhaochenhui 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx14 = document.getElementById('chart14').getContext('2d');
        new Chart(ctx14, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [20, 7, 9, 3, 2, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'guoyanlong 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx15 = document.getElementById('chart15').getContext('2d');
        new Chart(ctx15, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [17, 12, 2, 2, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'wangye07 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx16 = document.getElementById('chart16').getContext('2d');
        new Chart(ctx16, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [15, 6, 0, 5, 0, 1, 0, 0, 2],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'shiyu 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx17 = document.getElementById('chart17').getContext('2d');
        new Chart(ctx17, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [5, 6, 1, 6, 4, 2, 0, 0, 2],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'liruisheng 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx18 = document.getElementById('chart18').getContext('2d');
        new Chart(ctx18, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [12, 9, 3, 1, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'wb_louhao 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx19 = document.getElementById('chart19').getContext('2d');
        new Chart(ctx19, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [6, 3, 1, 8, 0, 0, 0, 1, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'liuyouchao 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx20 = document.getElementById('chart20').getContext('2d');
        new Chart(ctx20, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [4, 8, 2, 4, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'zouweicheng 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx21 = document.getElementById('chart21').getContext('2d');
        new Chart(ctx21, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [7, 0, 1, 4, 0, 1, 0, 2, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'wangjun07 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx22 = document.getElementById('chart22').getContext('2d');
        new Chart(ctx22, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [5, 1, 0, 0, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'wb_luoyang05 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx23 = document.getElementById('chart23').getContext('2d');
        new Chart(ctx23, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [1, 0, 0, 0, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'yechao 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx24 = document.getElementById('chart24').getContext('2d');
        new Chart(ctx24, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [1, 0, 0, 0, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'wb_zhanghongyu08 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx25 = document.getElementById('chart25').getContext('2d');
        new Chart(ctx25, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [0, 0, 0, 0, 1, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'fanlvlun 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx26 = document.getElementById('chart26').getContext('2d');
        new Chart(ctx26, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [0, 0, 0, 1, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'jiqiang 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        const ctx27 = document.getElementById('chart27').getContext('2d');
        new Chart(ctx27, {
            type: 'bar',
            data: {
                labels: ['≤2次', '3-5次', '6-10次', '11-50次', '51-100次', '101-300次', '301-500次', '501-1000次', '>1000次'],
                datasets: [{
                    label: '慢查数量',
                    data: [0, 0, 0, 0, 1, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(255, 154, 158, 0.8)',
                        'rgba(250, 208, 196, 0.8)',
                        'rgba(156, 204, 101, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(255, 87, 34, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(237, 100, 166, 1)',
                        'rgba(255, 154, 158, 1)',
                        'rgba(250, 208, 196, 1)',
                        'rgba(156, 204, 101, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(255, 87, 34, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'zangshaoqi 的执行次数分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

    </script>
</body>
</html>
